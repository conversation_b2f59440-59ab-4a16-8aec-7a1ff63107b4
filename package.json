{"name": "mirage-os", "version": "1.0.0", "description": "网页版操作系统内核模拟器", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:server": "nodemon server/app.js", "dev:client": "vite", "build": "vite build", "preview": "vite preview", "start": "node server/app.js"}, "dependencies": {"koa": "^2.14.2", "koa-router": "^12.0.0", "koa-static": "^5.0.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "ws": "^8.14.2"}, "devDependencies": {"@sveltejs/vite-plugin-svelte": "^3.0.1", "svelte": "^4.2.8", "vite": "^5.0.10", "nodemon": "^3.0.2", "concurrently": "^8.2.2"}}