# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 构建输出
dist/
build/
out/

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.cache/

# 测试覆盖率
coverage/
*.lcov

# 运行时文件
*.pid
*.seed
*.pid.lock

# 其他
.nyc_output
.grunt
bower_components
.lock-wscript
.wafpickle-N
.node_repl_history
*.tgz
.npm
.eslintcache
.node_repl_history
*.orig